#!/bin/bash

# Test Deployment Script
# This script simulates the GitHub Actions workflow locally for testing

set -e  # Exit on any error

echo "🧪 Testing Platform Config Deployment Workflow"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_step() {
    echo -e "${BLUE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Step 1: Validate input configs
print_step "🔍 Step 1: Validating input configuration files..."

required_files=("input.base.config.json" "schema.json")
for file in "${required_files[@]}"; do
    if [[ ! -f "$file" ]]; then
        print_error "Required file missing: $file"
        exit 1
    fi
done

# Find platform config files
platform_files=(input.*.config.json)
# Remove base config from array
for i in "${!platform_files[@]}"; do
    if [[ ${platform_files[i]} == "input.base.config.json" ]]; then
        unset 'platform_files[i]'
    fi
done

if [[ ${#platform_files[@]} -eq 0 ]]; then
    print_error "No platform config files found"
    exit 1
fi

print_success "Found platform configs: ${platform_files[*]}"

# Step 2: Check Python and dependencies
print_step "🐍 Step 2: Checking Python environment..."

if ! command -v python3 &> /dev/null; then
    print_error "Python 3 not found"
    exit 1
fi

if ! python3 -c "import jsonschema" 2>/dev/null; then
    print_warning "jsonschema not installed. Installing..."
    pip3 install jsonschema
fi

print_success "Python environment ready"

# Step 3: Clean previous output
print_step "🧹 Step 3: Cleaning previous output..."

if [[ -d "output" ]]; then
    rm -rf output
    print_success "Previous output cleaned"
else
    print_success "No previous output to clean"
fi

# Step 4: Merge platform configs
print_step "🔄 Step 4: Merging platform configurations..."

if python3 merge_configs.py; then
    print_success "Config merge completed successfully"
else
    print_error "Config merge failed"
    exit 1
fi

# Step 5: Verify output configs
print_step "🔍 Step 5: Verifying output configurations..."

if [[ ! -d "output" ]]; then
    print_error "Output directory not found"
    exit 1
fi

config_count=$(find output -name "*.config.json" | wc -l)
if [[ $config_count -eq 0 ]]; then
    print_error "No output config files generated"
    exit 1
fi

print_success "Generated $config_count platform config files"

if [[ ! -f "output/stats.md" ]]; then
    print_error "Stats file not generated"
    exit 1
fi

print_success "Stats file generated successfully"

# Step 6: Display results
print_step "📊 Step 6: Deployment Results"

echo ""
echo "Generated Config Files:"
for config_file in output/*.config.json; do
    if [[ -f "$config_file" ]]; then
        filename=$(basename "$config_file")
        platform=$(echo "$filename" | sed 's/.config.json//')
        size=$(stat -f%z "$config_file" 2>/dev/null || stat -c%s "$config_file" 2>/dev/null || echo "unknown")
        echo "  📱 $platform: $filename ($size bytes)"
    fi
done

echo ""
echo "Merge Statistics Summary:"
echo "------------------------"
head -15 output/stats.md

# Step 7: Simulate S3 upload (dry run)
print_step "☁️  Step 7: Simulating S3 upload (dry run)..."

echo "Files that would be uploaded to S3:"
for file in output/*.config.json output/stats.md; do
    if [[ -f "$file" ]]; then
        filename=$(basename "$file")
        echo "  📤 $filename"
    fi
done

print_success "S3 upload simulation completed"

# Final summary
echo ""
echo "🎉 Deployment Test Summary"
echo "========================="
print_success "All validation steps passed"
print_success "Config merge completed successfully"
print_success "Output files generated and verified"
print_success "Ready for S3 deployment"

echo ""
echo "💡 Next Steps:"
echo "  1. Set up AWS credentials and S3 buckets"
echo "  2. Configure GitHub repository secrets"
echo "  3. Create PR targeting deployment branch"
echo "  4. Merge PR to trigger automated deployment"

echo ""
echo "📖 See GITHUB_ACTIONS_SETUP.md for complete setup instructions"
