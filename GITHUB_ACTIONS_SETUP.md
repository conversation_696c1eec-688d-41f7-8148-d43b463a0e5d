# GitHub Actions Setup Guide

This guide explains how to set up the automated platform config deployment using GitHub Actions.

## Overview

The GitHub Actions workflow automatically:
1. **Triggers** on PR merges to deployment branches (`prod-deploy`, `qa-deploy`, `dev-deploy`)
2. **Merges** base and platform-specific configs using the Python merger script
3. **Validates** all configurations against the JSON schema
4. **Uploads** merged configs to environment-specific S3 buckets
5. **Verifies** successful deployment and provides detailed reporting

## Workflow Triggers

The workflow runs when:
- **Pull Requests are merged** into deployment branches
- **Direct pushes** are made to deployment branches

### Deployment Branches
- `prod-deploy` → Production environment
- `qa-deploy` → QA environment  
- `dev-deploy` → Development environment

## Prerequisites

### 1. AWS Setup

#### Create S3 Buckets
Create separate S3 buckets for each environment:
```bash
# Example bucket names
brainivy-configs-prod
brainivy-configs-qa
brainivy-configs-dev
```

#### IAM User/Role Setup
Create an IAM user or role with the following policy:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject",
        "s3:ListBucket"
      ],
      "Resource": [
        "arn:aws:s3:::brainivy-configs-*",
        "arn:aws:s3:::brainivy-configs-*/*"
      ]
    }
  ]
}
```

### 2. GitHub Repository Secrets

Add the following secrets to your GitHub repository:

#### Required Secrets
| Secret Name | Description | Example |
|-------------|-------------|---------|
| `AWS_ACCESS_KEY_ID` | AWS access key ID | `AKIAIOSFODNN7EXAMPLE` |
| `AWS_SECRET_ACCESS_KEY` | AWS secret access key | `wJalrXUtnFEMI/K7MDENG/...` |
| `AWS_REGION` | AWS region (optional) | `us-west-2` |
| `PROD_S3_BUCKET` | Production S3 bucket name | `brainivy-configs-prod` |
| `QA_S3_BUCKET` | QA S3 bucket name | `brainivy-configs-qa` |
| `DEV_S3_BUCKET` | Development S3 bucket name | `brainivy-configs-dev` |

#### Adding Secrets
1. Go to your GitHub repository
2. Navigate to **Settings** → **Secrets and variables** → **Actions**
3. Click **New repository secret**
4. Add each secret from the table above

## Workflow Steps

### 1. Environment Detection
The workflow automatically detects the target environment based on the branch:
- `prod-deploy` → Production
- `qa-deploy` → QA
- `dev-deploy` → Development

### 2. Config Validation
- Validates all input config files exist
- Checks JSON schema compliance
- Ensures required files are present

### 3. Config Merging
- Runs the Python merger script
- Merges base config with platform-specific configs
- Generates timestamped output files

### 4. Output Verification
- Verifies output directory and files exist
- Validates generated config files
- Checks statistics report generation

### 5. S3 Deployment
- Configures AWS credentials
- Uploads configs to environment-specific S3 paths
- Verifies successful upload

### 6. Deployment Summary
- Creates detailed deployment summary
- Shows deployed configs and statistics
- Provides S3 location information

## S3 Structure

Configs are uploaded to S3 with the following structure:

```
s3://bucket-name/
└── configs/
    └── {environment}/
        ├── ios.config.json
        ├── android.config.json
        └── stats.md
```

### Example Paths
- **Production**: `s3://brainivy-configs-prod/configs/production/`
- **QA**: `s3://brainivy-configs-qa/configs/qa/`
- **Development**: `s3://brainivy-configs-dev/configs/development/`

## Usage Examples

### Deploying to Development
1. Create a PR targeting `dev-deploy` branch
2. Merge the PR
3. Workflow automatically runs and deploys to development S3 bucket

### Deploying to Production
1. Create a PR targeting `prod-deploy` branch
2. Get PR reviewed and approved
3. Merge the PR
4. Workflow automatically runs and deploys to production S3 bucket

## Monitoring and Troubleshooting

### Viewing Workflow Runs
1. Go to your GitHub repository
2. Click the **Actions** tab
3. Select the "Deploy Platform Configs" workflow
4. View individual run details and logs

### Common Issues

#### Authentication Errors
- **Cause**: Invalid AWS credentials
- **Solution**: Verify AWS secrets are correctly set in GitHub

#### S3 Upload Failures
- **Cause**: Insufficient S3 permissions or invalid bucket names
- **Solution**: Check IAM policy and bucket names in secrets

#### Config Validation Errors
- **Cause**: Invalid JSON or schema violations
- **Solution**: Review config files and ensure they match the schema

#### Missing Platform Configs
- **Cause**: No platform config files found
- **Solution**: Ensure `input.*.config.json` files exist (except base)

### Workflow Outputs

The workflow provides detailed outputs:
- **Step-by-step logs** for debugging
- **Deployment summary** with config details
- **S3 verification** confirming successful uploads
- **Statistics report** showing merge details

## Security Considerations

1. **AWS Credentials**: Store as GitHub secrets, never in code
2. **S3 Bucket Access**: Use least-privilege IAM policies
3. **Branch Protection**: Consider requiring PR reviews for deployment branches
4. **Environment Separation**: Use separate buckets for each environment

## Customization

### Adding New Environments
1. Create a new deployment branch (e.g., `staging-deploy`)
2. Add the branch to the workflow trigger
3. Add environment detection logic
4. Create corresponding S3 bucket and secret

### Modifying S3 Paths
Edit the `s3_path` values in the workflow's environment detection step.

### Adding Notifications
Consider adding Slack, email, or other notification steps to the workflow for deployment alerts.
