# Environment Configuration for Platform Config Deployment
# This file documents the environment-specific settings used in the GitHub Actions workflow

environments:
  production:
    branch: prod-deploy
    s3_bucket_secret: PROD_S3_BUCKET
    s3_path: configs/production
    description: Production environment for live applications
    
  qa:
    branch: qa-deploy
    s3_bucket_secret: QA_S3_BUCKET
    s3_path: configs/qa
    description: QA environment for testing and validation
    
  development:
    branch: dev-deploy
    s3_bucket_secret: DEV_S3_BUCKET
    s3_path: configs/development
    description: Development environment for ongoing development

# Required GitHub Secrets:
required_secrets:
  # AWS Configuration
  - AWS_ACCESS_KEY_ID      # AWS access key with S3 permissions
  - AWS_SECRET_ACCESS_KEY  # AWS secret access key
  - AWS_REGION            # AWS region (optional, defaults to us-east-1)
  
  # S3 Bucket Names (one for each environment)
  - PROD_S3_BUCKET        # Production S3 bucket name
  - QA_S3_BUCKET          # QA S3 bucket name  
  - DEV_S3_BUCKET         # Development S3 bucket name

# S3 Bucket Structure:
# Each bucket will have the following structure:
# bucket-name/
# └── configs/
#     └── {environment}/
#         ├── ios.config.json
#         ├── android.config.json
#         └── stats.md

# IAM Policy Requirements:
# The AWS user/role needs the following S3 permissions:
# {
#   "Version": "2012-10-17",
#   "Statement": [
#     {
#       "Effect": "Allow",
#       "Action": [
#         "s3:GetObject",
#         "s3:PutObject",
#         "s3:DeleteObject",
#         "s3:ListBucket"
#       ],
#       "Resource": [
#         "arn:aws:s3:::your-bucket-name",
#         "arn:aws:s3:::your-bucket-name/*"
#       ]
#     }
#   ]
# }
