name: Deploy Platform Configs

on:
  push:
    branches:
      - prod-deploy
      - qa-deploy
      - dev-deploy
  pull_request:
    branches:
      - prod-deploy
      - qa-deploy
      - dev-deploy
    types: [closed]

env:
  PYTHON_VERSION: '3.9'
  CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}

jobs:
  deploy-configs:
    name: Merge and Deploy Platform Configs
    runs-on: ubuntu-latest
    
    # Only run on merged PRs or direct pushes to deployment branches
    if: github.event_name == 'push' || (github.event.pull_request.merged == true)
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install jsonschema
          
      - name: Determine environment
        id: env
        run: |
          BRANCH_NAME="${{ github.ref_name }}"
          case $BRANCH_NAME in
            prod-deploy)
              echo "environment=production" >> $GITHUB_OUTPUT
              echo "r2_bucket=brainivy-configs-prod" >> $GITHUB_OUTPUT
              echo "r2_path=configs/production" >> $GITHUB_OUTPUT
              ;;
            qa-deploy)
              echo "environment=qa" >> $GITHUB_OUTPUT
              echo "r2_bucket=brainivy-configs-qa" >> $GITHUB_OUTPUT
              echo "r2_path=configs/qa" >> $GITHUB_OUTPUT
              ;;
            dev-deploy)
              echo "environment=development" >> $GITHUB_OUTPUT
              echo "r2_bucket=brainivy-configs-dev" >> $GITHUB_OUTPUT
              echo "r2_path=configs/development" >> $GITHUB_OUTPUT
              ;;
            *)
              echo "Unknown branch: $BRANCH_NAME"
              exit 1
              ;;
          esac
          
      - name: Validate input configs
        run: |
          echo "🔍 Validating input configuration files..."
          
          # Check if required files exist
          required_files=("input.base.config.json" "schema.json")
          for file in "${required_files[@]}"; do
            if [[ ! -f "$file" ]]; then
              echo "❌ Required file missing: $file"
              exit 1
            fi
          done
          
          # Find platform config files
          platform_files=(input.*.config.json)
          platform_files=("${platform_files[@]/input.base.config.json/}")  # Remove base config
          
          if [[ ${#platform_files[@]} -eq 0 ]]; then
            echo "❌ No platform config files found"
            exit 1
          fi
          
          echo "✅ Found platform configs: ${platform_files[*]}"
          
      - name: Merge platform configs
        run: |
          echo "🔄 Merging platform configurations..."
          python merge_configs.py
          
          # Check if merge was successful
          if [[ $? -ne 0 ]]; then
            echo "❌ Config merge failed"
            exit 1
          fi
          
          echo "✅ Config merge completed successfully"
          
      - name: Verify output configs
        run: |
          echo "🔍 Verifying output configurations..."
          
          # Check if output directory exists and has files
          if [[ ! -d "output" ]]; then
            echo "❌ Output directory not found"
            exit 1
          fi
          
          # Count output config files
          config_count=$(find output -name "*.config.json" | wc -l)
          if [[ $config_count -eq 0 ]]; then
            echo "❌ No output config files generated"
            exit 1
          fi
          
          echo "✅ Generated $config_count platform config files"
          
          # Verify stats file exists
          if [[ ! -f "output/stats.md" ]]; then
            echo "❌ Stats file not generated"
            exit 1
          fi
          
          echo "✅ Stats file generated successfully"
          
          # Display stats summary
          echo "📊 Merge Statistics:"
          head -20 output/stats.md
          
      - name: Install Cloudflare CLI and AWS CLI
        run: |
          # Install wrangler CLI for R2 operations
          npm install -g wrangler

          # Install AWS CLI for S3-compatible operations (backup method)
          curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
          unzip awscliv2.zip
          sudo ./aws/install

      - name: Configure Cloudflare credentials
        run: |
          # Set up Cloudflare API token for wrangler
          echo "CLOUDFLARE_API_TOKEN=${{ secrets.CLOUDFLARE_API_TOKEN }}" >> $GITHUB_ENV
          echo "CLOUDFLARE_ACCOUNT_ID=${{ secrets.CLOUDFLARE_ACCOUNT_ID }}" >> $GITHUB_ENV

          # Verify wrangler can authenticate
          echo "🔐 Verifying Cloudflare authentication..."
          if wrangler whoami; then
            echo "✅ Authentication successful"
          else
            echo "❌ Authentication failed"
            exit 1
          fi

          # List R2 buckets to verify access
          echo "📦 Listing R2 buckets..."
          wrangler r2 bucket list --remote

      - name: Upload configs to Cloudflare R2
        run: |
          echo "☁️ Uploading configs to Cloudflare R2..."
          echo "Environment: ${{ steps.env.outputs.environment }}"
          echo "R2 Bucket: ${{ steps.env.outputs.r2_bucket }}"
          echo "R2 Path: ${{ steps.env.outputs.r2_path }}"

          # Upload each config file to R2
          for config_file in output/*.config.json output/stats.md; do
            if [[ -f "$config_file" ]]; then
              filename=$(basename "$config_file")
              echo "📤 Uploading $filename..."

              # Set appropriate content type
              if [[ "$filename" == *.json ]]; then
                content_type="application/json"
              elif [[ "$filename" == *.md ]]; then
                content_type="text/markdown"
              else
                content_type="application/octet-stream"
              fi

              wrangler r2 object put "${{ steps.env.outputs.r2_bucket }}/${{ steps.env.outputs.r2_path }}/$filename" \
                --file "$config_file" \
                --content-type "$content_type" \
                --remote

              if [[ $? -eq 0 ]]; then
                echo "✅ $filename uploaded successfully"
              else
                echo "❌ Failed to upload $filename"
                exit 1
              fi
            fi
          done

          echo "✅ Upload completed successfully"

      - name: Verify R2 upload
        run: |
          echo "🔍 Verifying R2 upload..."

          # Verify each config file was uploaded by attempting to download it
          echo "📁 Verifying uploaded files:"
          for config_file in output/*.config.json output/stats.md; do
            if [[ -f "$config_file" ]]; then
              filename=$(basename "$config_file")
              echo "🔍 Checking $filename..."

              # Try to get the file from R2 (download to /tmp to verify it exists)
              if wrangler r2 object get "${{ steps.env.outputs.r2_bucket }}/${{ steps.env.outputs.r2_path }}/$filename" --file "/tmp/verify_$filename" --remote > /dev/null 2>&1; then
                echo "✅ $filename verified in R2"
                rm -f "/tmp/verify_$filename"  # Clean up temp file
              else
                echo "❌ $filename verification failed"
                exit 1
              fi
            fi
          done

          echo "🎉 All configs verified in R2!"
          
      - name: Create deployment summary
        run: |
          echo "## 🚀 Config Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Environment:** ${{ steps.env.outputs.environment }}" >> $GITHUB_STEP_SUMMARY
          echo "**Branch:** ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
          echo "**R2 Bucket:** ${{ steps.env.outputs.r2_bucket }}" >> $GITHUB_STEP_SUMMARY
          echo "**R2 Path:** ${{ steps.env.outputs.r2_path }}/" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 📱 Deployed Configs" >> $GITHUB_STEP_SUMMARY
          for config_file in output/*.config.json; do
            if [[ -f "$config_file" ]]; then
              filename=$(basename "$config_file")
              platform=$(echo "$filename" | sed 's/.config.json//')
              echo "- **$platform**: $filename" >> $GITHUB_STEP_SUMMARY
            fi
          done
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 🌐 Public Access" >> $GITHUB_STEP_SUMMARY
          echo "Once public access is enabled, configs will be available at:" >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
          echo "https://pub-[hash].r2.dev/${{ steps.env.outputs.r2_path }}/[config-file]" >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 📊 Merge Statistics" >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
          head -15 output/stats.md >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
