name: Deploy Platform Configs

on:
  push:
    branches:
      - prod-deploy
      - qa-deploy
      - dev-deploy
  pull_request:
    branches:
      - prod-deploy
      - qa-deploy
      - dev-deploy
    types: [closed]

env:
  PYTHON_VERSION: '3.9'

jobs:
  deploy-configs:
    name: Merge and Deploy Platform Configs
    runs-on: ubuntu-latest
    
    # Only run on merged PRs or direct pushes to deployment branches
    if: github.event_name == 'push' || (github.event.pull_request.merged == true)
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install jsonschema
          
      - name: Determine environment
        id: env
        run: |
          BRANCH_NAME="${{ github.ref_name }}"
          case $BRANCH_NAME in
            prod-deploy)
              echo "environment=production" >> $GITHUB_OUTPUT
              echo "s3_bucket=${{ secrets.PROD_S3_BUCKET }}" >> $GITHUB_OUTPUT
              echo "s3_path=configs/production" >> $GITHUB_OUTPUT
              ;;
            qa-deploy)
              echo "environment=qa" >> $GITHUB_OUTPUT
              echo "s3_bucket=${{ secrets.QA_S3_BUCKET }}" >> $GITHUB_OUTPUT
              echo "s3_path=configs/qa" >> $GITHUB_OUTPUT
              ;;
            dev-deploy)
              echo "environment=development" >> $GITHUB_OUTPUT
              echo "s3_bucket=${{ secrets.DEV_S3_BUCKET }}" >> $GITHUB_OUTPUT
              echo "s3_path=configs/development" >> $GITHUB_OUTPUT
              ;;
            *)
              echo "Unknown branch: $BRANCH_NAME"
              exit 1
              ;;
          esac
          
      - name: Validate input configs
        run: |
          echo "🔍 Validating input configuration files..."
          
          # Check if required files exist
          required_files=("input.base.config.json" "schema.json")
          for file in "${required_files[@]}"; do
            if [[ ! -f "$file" ]]; then
              echo "❌ Required file missing: $file"
              exit 1
            fi
          done
          
          # Find platform config files
          platform_files=(input.*.config.json)
          platform_files=("${platform_files[@]/input.base.config.json/}")  # Remove base config
          
          if [[ ${#platform_files[@]} -eq 0 ]]; then
            echo "❌ No platform config files found"
            exit 1
          fi
          
          echo "✅ Found platform configs: ${platform_files[*]}"
          
      - name: Merge platform configs
        run: |
          echo "🔄 Merging platform configurations..."
          python merge_configs.py
          
          # Check if merge was successful
          if [[ $? -ne 0 ]]; then
            echo "❌ Config merge failed"
            exit 1
          fi
          
          echo "✅ Config merge completed successfully"
          
      - name: Verify output configs
        run: |
          echo "🔍 Verifying output configurations..."
          
          # Check if output directory exists and has files
          if [[ ! -d "output" ]]; then
            echo "❌ Output directory not found"
            exit 1
          fi
          
          # Count output config files
          config_count=$(find output -name "*.config.json" | wc -l)
          if [[ $config_count -eq 0 ]]; then
            echo "❌ No output config files generated"
            exit 1
          fi
          
          echo "✅ Generated $config_count platform config files"
          
          # Verify stats file exists
          if [[ ! -f "output/stats.md" ]]; then
            echo "❌ Stats file not generated"
            exit 1
          fi
          
          echo "✅ Stats file generated successfully"
          
          # Display stats summary
          echo "📊 Merge Statistics:"
          head -20 output/stats.md
          
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION || 'us-east-1' }}
          
      - name: Upload configs to S3
        run: |
          echo "☁️ Uploading configs to S3..."
          echo "Environment: ${{ steps.env.outputs.environment }}"
          echo "S3 Bucket: ${{ steps.env.outputs.s3_bucket }}"
          echo "S3 Path: ${{ steps.env.outputs.s3_path }}"
          
          # Upload all config files
          aws s3 sync output/ s3://${{ steps.env.outputs.s3_bucket }}/${{ steps.env.outputs.s3_path }}/ \
            --exclude "*" \
            --include "*.config.json" \
            --include "stats.md" \
            --delete
          
          echo "✅ Upload completed successfully"
          
      - name: Verify S3 upload
        run: |
          echo "🔍 Verifying S3 upload..."
          
          # List uploaded files
          echo "📁 Files in S3:"
          aws s3 ls s3://${{ steps.env.outputs.s3_bucket }}/${{ steps.env.outputs.s3_path }}/ --recursive
          
          # Verify each config file was uploaded
          for config_file in output/*.config.json; do
            if [[ -f "$config_file" ]]; then
              filename=$(basename "$config_file")
              if aws s3 ls s3://${{ steps.env.outputs.s3_bucket }}/${{ steps.env.outputs.s3_path }}/$filename > /dev/null; then
                echo "✅ $filename uploaded successfully"
              else
                echo "❌ $filename upload failed"
                exit 1
              fi
            fi
          done
          
          echo "🎉 All configs verified in S3!"
          
      - name: Create deployment summary
        run: |
          echo "## 🚀 Config Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Environment:** ${{ steps.env.outputs.environment }}" >> $GITHUB_STEP_SUMMARY
          echo "**Branch:** ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
          echo "**S3 Location:** s3://${{ steps.env.outputs.s3_bucket }}/${{ steps.env.outputs.s3_path }}/" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 📱 Deployed Configs" >> $GITHUB_STEP_SUMMARY
          for config_file in output/*.config.json; do
            if [[ -f "$config_file" ]]; then
              filename=$(basename "$config_file")
              platform=$(echo "$filename" | sed 's/.config.json//')
              echo "- **$platform**: $filename" >> $GITHUB_STEP_SUMMARY
            fi
          done
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 📊 Merge Statistics" >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
          head -15 output/stats.md >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
