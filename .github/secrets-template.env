# GitHub Secrets Template for Platform Config Deployment
# Copy this file and fill in your actual values, then add them as GitHub repository secrets

# AWS Configuration
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_REGION=us-east-1

# S3 Bucket Names for Each Environment
PROD_S3_BUCKET=your-production-bucket-name
QA_S3_BUCKET=your-qa-bucket-name
DEV_S3_BUCKET=your-development-bucket-name

# Example values:
# AWS_ACCESS_KEY_ID=AKIAIOSFODNN7EXAMPLE
# AWS_SECRET_ACCESS_KEY=wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
# AWS_REGION=us-west-2
# PROD_S3_BUCKET=brainivy-configs-prod
# QA_S3_BUCKET=brainivy-configs-qa
# DEV_S3_BUCKET=brainivy-configs-dev
