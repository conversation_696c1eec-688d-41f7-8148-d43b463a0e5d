# Platform Config Merger

A Python script that merges base and platform-specific configuration files, validates them against a JSON schema, and outputs merged configs with detailed statistics.

## Features

- 🔄 **Hierarchical Config Merging**: Merges base config with platform-specific overrides
- ✅ **JSON Schema Validation**: Validates all configs against a defined schema
- 📊 **Detailed Statistics**: Generates comprehensive merge reports
- 🚀 **Auto-Discovery**: Automatically discovers available platform configs
- 🛡️ **Error Handling**: Robust error handling with detailed logging

## Directory Structure

```
platform-configs/
├── input.base.config.json      # Base configuration (shared across platforms)
├── input.ios.config.json       # iOS-specific configuration
├── input.android.config.json   # Android-specific configuration
├── schema.json                 # JSON schema for validation
├── merge_configs.py            # Main merger script
├── output/                     # Generated output directory
│   ├── ios.config.json        # Merged iOS configuration
│   ├── android.config.json    # Merged Android configuration
│   └── stats.md               # Merge statistics report
└── README.md                  # This file
```

## Usage

### Prerequisites

Install the required Python package for JSON schema validation:

```bash
pip install jsonschema
```

### Running the Merger

Simply run the main script:

```bash
python3 merge_configs.py
```

The script will:
1. Discover all platform configs (files matching `input.*.config.json` except base)
2. Load and validate the base config
3. For each platform:
   - Load and validate the platform-specific config
   - Merge it with the base config (platform settings override base)
   - Validate the merged result
   - Write the output to `output/{platform}.config.json`
4. Generate a detailed statistics report in `output/stats.md`

### Configuration Format

All config files must follow this structure:

```json
{
  "name": "platform-name",
  "modified": "2025-06-26T00:00:00Z",
  "config": {
    "app": { /* app-specific settings */ },
    "api": { /* API configuration */ },
    "features": { /* feature flags */ },
    "ui": { /* UI configuration */ },
    "platform": { /* platform-specific settings */ }
  }
}
```

### Adding New Platforms

To add a new platform:

1. Create a new config file: `input.{platform}.config.json`
2. Follow the JSON schema structure
3. Run the merger script

The script will automatically discover and process the new platform.

### Schema Validation

The `schema.json` file defines the structure and validation rules for all configs. The schema supports:

- Required fields validation
- Type checking
- Format validation (URLs, dates)
- Nested object validation

## Example Output

The merger produces clean, validated JSON files and a comprehensive stats report:

```markdown
# Config Merge Statistics

## Summary
- **Start Time**: 2025-06-26T11:07:35.664559
- **End Time**: 2025-06-26T11:07:35.665439
- **Duration**: 0.01 seconds
- **Platforms Processed**: 2
- **Errors**: 0
- **Warnings**: 0

## Processed Platforms
- **android**: ✅ Success → `output/android.config.json`
- **ios**: ✅ Success → `output/ios.config.json`
```

## Development

### Testing

Run the validation test to ensure everything is working:

```bash
python3 test_validation.py
```

### Extending

The system is designed to be easily extensible:

- Add new platforms by creating new input files
- Extend the schema to support new configuration options
- Modify the merger logic for custom merge strategies

## Error Handling

The script provides comprehensive error handling:

- Invalid JSON files are reported with specific error messages
- Schema validation failures include detailed validation errors
- File I/O errors are caught and logged
- The process continues even if individual platforms fail

All errors and warnings are logged in the statistics report for easy debugging.
