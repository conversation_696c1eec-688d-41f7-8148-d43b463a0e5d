# Setup Checklist for Cloudflare R2 Config Deployment

## ✅ Completed

- [x] **R2 Buckets Created**: All three buckets are ready
  - `brainivy-configs-prod`
  - `brainivy-configs-qa`
  - `brainivy-configs-dev`
- [x] **GitHub Actions Workflow**: Updated to use Cloudflare R2
- [x] **Documentation**: All guides updated for Cloudflare R2
- [x] **Test Script**: Local testing script works correctly

## 🔲 Next Steps (Manual Setup Required)

### 1. Enable Public Access for R2 Buckets

For each bucket, you need to enable public access in the Cloudflare dashboard:

**For each bucket (`brainivy-configs-prod`, `brainivy-configs-qa`, `brainivy-configs-dev`):**

1. Go to https://dash.cloudflare.com
2. Navigate to **R2 Object Storage**
3. Click on the bucket name
4. Go to **Settings** tab
5. Under **Public Development URL**, click **Enable**
6. Type `allow` and click **Allow**
7. **Copy the public URL** (format: `https://pub-[hash].r2.dev`)

### 2. Create Cloudflare API Token

1. Go to https://dash.cloudflare.com/profile/api-tokens
2. Click **Create Token**
3. Use **Custom token** template
4. Add permissions: **Account:Cloudflare R2:Edit**
5. Add account resource: **Include your account**
6. Continue and **copy the token**

### 3. Configure GitHub Repository Secrets

Add these secrets to your GitHub repository:

| Secret Name | Value | Where to Find |
|-------------|-------|---------------|
| `CLOUDFLARE_API_TOKEN` | Your API token from step 2 | From API token creation |
| `CLOUDFLARE_ACCOUNT_ID` | `e59a460617f2a48be73bb62130419458` | Already provided |

**To add secrets:**
1. Go to your GitHub repository
2. Navigate to **Settings** → **Secrets and variables** → **Actions**
3. Click **New repository secret**
4. Add each secret from the table above

### 4. Test the Deployment

1. **Create a test branch**: `git checkout -b test-dev-deploy`
2. **Make a small change**: Edit a config file or add a comment
3. **Create PR**: Target the `dev-deploy` branch
4. **Merge PR**: This will trigger the GitHub Actions workflow
5. **Check workflow**: Go to Actions tab to see if it runs successfully
6. **Verify deployment**: Check if configs appear in the R2 bucket

## 📋 Expected Results

After completing the setup:

### Public URLs
Your configs will be accessible at URLs like:
```
https://pub-[hash].r2.dev/configs/production/ios.config.json
https://pub-[hash].r2.dev/configs/qa/android.config.json
https://pub-[hash].r2.dev/configs/development/stats.md
```

### Deployment Branches
- **`prod-deploy`** → `brainivy-configs-prod` bucket
- **`qa-deploy`** → `brainivy-configs-qa` bucket
- **`dev-deploy`** → `brainivy-configs-dev` bucket

### Workflow Triggers
- PR merges to deployment branches
- Direct pushes to deployment branches

## 🔧 Troubleshooting

### Common Issues

1. **"Authentication failed"**
   - Check API token is correct and has R2 permissions
   - Verify account ID matches your Cloudflare account

2. **"Bucket not found"**
   - Confirm bucket names are correct in workflow
   - Verify buckets exist in your Cloudflare account

3. **"Public access denied"**
   - Ensure public development URL is enabled for each bucket
   - Check bucket settings in Cloudflare dashboard

4. **"Workflow not triggering"**
   - Verify branch names match exactly (`prod-deploy`, `qa-deploy`, `dev-deploy`)
   - Check PR is targeting the correct branch

### Getting Help

- **Workflow logs**: Check GitHub Actions tab for detailed error messages
- **Local testing**: Run `./test-deployment.sh` to test config merging locally
- **Documentation**: See `GITHUB_ACTIONS_SETUP.md` for detailed setup instructions

## 🎯 Success Criteria

You'll know the setup is working when:

1. ✅ GitHub Actions workflow runs without errors
2. ✅ Config files appear in the correct R2 bucket
3. ✅ Public URLs return the config files
4. ✅ Deployment summary shows successful upload
5. ✅ Stats report is generated and accessible

## 📞 Next Actions

1. **Enable public access** for all three R2 buckets
2. **Create API token** with R2 permissions
3. **Add GitHub secrets** for API token and account ID
4. **Test deployment** with a PR to `dev-deploy` branch
5. **Verify public access** by accessing the config URLs

Once these steps are complete, your automated config deployment system will be fully operational!
