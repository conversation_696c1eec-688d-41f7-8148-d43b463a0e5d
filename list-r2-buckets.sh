#!/bin/bash

# Cloudflare R2 Bucket Lister
# This script lists all R2 buckets in your Cloudflare account

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if required environment variables are set
if [[ -z "$CLOUDFLARE_API_TOKEN" ]]; then
    print_error "CLOUDFLARE_API_TOKEN environment variable is not set"
    echo ""
    echo "To get your API token:"
    echo "1. Go to https://dash.cloudflare.com/profile/api-tokens"
    echo "2. Click 'Create Token'"
    echo "3. Use 'Custom token' template"
    echo "4. Add permissions: Account:Cloudflare R2:Read"
    echo "5. Set the token: export CLOUDFLARE_API_TOKEN='your-token-here'"
    echo ""
    exit 1
fi

if [[ -z "$CLOUDFLARE_ACCOUNT_ID" ]]; then
    print_error "CLOUDFLARE_ACCOUNT_ID environment variable is not set"
    echo ""
    echo "To get your Account ID:"
    echo "1. Go to https://dash.cloudflare.com"
    echo "2. Select any domain or go to R2"
    echo "3. Look for 'Account ID' in the right sidebar"
    echo "4. Set the account ID: export CLOUDFLARE_ACCOUNT_ID='your-account-id-here'"
    echo ""
    exit 1
fi

print_info "Listing R2 buckets for account: $CLOUDFLARE_ACCOUNT_ID"
echo ""

# Make API request to list R2 buckets
response=$(curl -s -X GET \
    "https://api.cloudflare.com/client/v4/accounts/$CLOUDFLARE_ACCOUNT_ID/r2/buckets" \
    -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
    -H "Content-Type: application/json")

# Check if curl command was successful
if [[ $? -ne 0 ]]; then
    print_error "Failed to make API request"
    exit 1
fi

# Parse the response
success=$(echo "$response" | jq -r '.success // false')

if [[ "$success" != "true" ]]; then
    print_error "API request failed"
    echo "Response: $response"
    exit 1
fi

# Extract bucket information
buckets=$(echo "$response" | jq -r '.result[]')

if [[ -z "$buckets" || "$buckets" == "null" ]]; then
    print_warning "No R2 buckets found in your account"
    exit 0
fi

# Display buckets
print_success "Found R2 buckets:"
echo ""

echo "$response" | jq -r '.result[] | "🪣 \(.name) (Created: \(.creation_date))"'

echo ""
print_info "Total buckets: $(echo "$response" | jq -r '.result | length')"

# Suggest bucket names for the GitHub Actions workflow
echo ""
print_info "Suggested bucket names for your deployment environments:"
echo "  • Production: brainivy-configs-prod"
echo "  • QA: brainivy-configs-qa" 
echo "  • Development: brainivy-configs-dev"
echo ""
print_info "If you need to create buckets, you can do so in the Cloudflare dashboard or using the API"
