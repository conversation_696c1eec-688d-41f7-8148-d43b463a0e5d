{"name": "android", "modified": "2025-06-26T11:10:22.317484Z", "config": {"app": {"name": "BrainIvy Platform", "version": "1.0.0", "environment": "production", "packageName": "com.brainivy.platform.android", "minSdkVersion": 21, "targetSdkVersion": 34}, "api": {"baseUrl": "https://api.brainivy.com", "timeout": 30000, "retries": 3}, "features": {"analytics": true, "crashReporting": true, "pushNotifications": true, "fingerprint": true, "adaptiveIcon": true}, "ui": {"theme": "material", "primaryColor": "#1976D2", "secondaryColor": "#34C759", "statusBarColor": "#1565C0"}, "platform": {"storeUrl": "https://play.google.com/store/apps/details?id=com.brainivy.platform", "reviewPrompt": true, "permissions": ["android.permission.INTERNET", "android.permission.CAMERA", "android.permission.USE_FINGERPRINT"]}}}